'use client';

import { useEffect, useRef, useState } from 'react';

import { BookData, readBooksFromExcel } from '@/lib/excelUtils';

interface BooksSectionProps {
  title?: string;
  documentPath?: string;
}

export default function BooksSection({ title = 'Books', documentPath = '/v2/doc/book.docx' }: BooksSectionProps) {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [booksData, setBooksData] = useState<BookData[]>([]);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Loading book document from:', documentPath);
        console.log('Loading books data from Excel...');

        // 同时加载 Word 文档和 Excel 数据
        const [documentResponse, excelData] = await Promise.all([fetch(documentPath), readBooksFromExcel()]);

        if (!documentResponse.ok) {
          throw new Error(`Failed to fetch document: ${documentResponse.status} ${documentResponse.statusText}`);
        }

        console.log('Book document fetched successfully, converting to array buffer...');
        console.log('Excel data loaded:', excelData.length, 'books');
        setBooksData(excelData);

        const arrayBuffer = await documentResponse.arrayBuffer();
        console.log('Array buffer size:', arrayBuffer.byteLength);

        // 动态导入 mammoth 以避免服务器端问题
        const mammoth = await import('mammoth');

        // 配置 mammoth 选项以更好地处理 Word 文档
        const options = {
          convertImage: mammoth.images.imgElement((image: any) => {
            return image.read('base64').then((imageBuffer: string) => {
              return {
                src: 'data:' + image.contentType + ';base64,' + imageBuffer,
              };
            });
          }),
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Heading 2'] => h2:fresh",
            "p[style-name='Heading 3'] => h3:fresh",
            "p[style-name='Title'] => h1.title:fresh",
            "r[style-name='Strong'] => strong",
            "r[style-name='Emphasis'] => em",
          ],
        };

        console.log('Converting book document with mammoth...');
        const result = await mammoth.convertToHtml({ arrayBuffer }, options);
        console.log('Mammoth conversion completed for books');

        if (result.messages && result.messages.length > 0) {
          console.warn('Mammoth conversion messages:', result.messages);
        }

        // 处理和清理 HTML 内容
        let processedContent = result.value;

        // 移除空段落
        processedContent = processedContent.replace(/<p>\s*<\/p>/g, '');

        // 处理列表项
        processedContent = processedContent.replace(
          /<p>(\d+\.?\s*)(.*?)<\/p>/g,
          '<p><span class="book-number">$1</span>$2</p>'
        );

        // 包装内容以应用自定义样式
        setContent(`<div class="books-document-content">${processedContent}</div>`);
      } catch (error) {
        console.error('Error loading book document:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setError(`Failed to load document: ${errorMessage}`);
        setContent('');
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [documentPath]);

  // 处理 Word 文档中的特殊格式
  useEffect(() => {
    if (contentRef.current && content && !loading) {
      const container = contentRef.current;

      // 处理编号列表
      const paragraphs = container.querySelectorAll('p');
      paragraphs.forEach((p) => {
        const text = p.textContent || '';
        const numberMatch = text.match(/^(\d+\.?\s*)/);

        if (numberMatch && !p.querySelector('.book-number')) {
          // 创建编号元素
          const numberSpan = document.createElement('span');
          numberSpan.className = 'book-number';
          numberSpan.textContent = numberMatch[1] || '';

          // 更新段落内容
          p.innerHTML = p.innerHTML.replace(numberMatch[0], '');
          p.insertBefore(numberSpan, p.firstChild);
        }
      });

      // 处理作者名称加粗（GuanHua Chen）
      const textNodes = container.querySelectorAll('*');
      textNodes.forEach((node) => {
        if (node.textContent && node.textContent.includes('GuanHua Chen')) {
          node.innerHTML = node.innerHTML.replace(
            /GuanHua Chen/g,
            '<strong class="author-highlight">GuanHua Chen</strong>'
          );
        }
      });

      // 处理书籍名称斜体
      const bookPattern =
        /([A-Z][a-zA-Z\s&:]+(?:Book|Handbook|Manual|Guide|Encyclopedia|Dictionary|Proceedings|Conference|Symposium|Workshop))/g;
      paragraphs.forEach((p) => {
        if (p.innerHTML && !p.querySelector('em.book-name')) {
          p.innerHTML = p.innerHTML.replace(bookPattern, '<em class="book-name">$1</em>');
        }
      });
    }
  }, [content, loading]);

  // 处理 Excel 数据并添加链接功能
  useEffect(() => {
    if (contentRef.current && content && booksData.length > 0 && !loading) {
      const container = contentRef.current;
      const paragraphs = container.querySelectorAll('p');

      paragraphs.forEach((p) => {
        const text = p.textContent || '';
        const numberMatch = text.match(/^(\d+)\.?\s*/);

        if (numberMatch && numberMatch[1]) {
          const bookIndex = parseInt(numberMatch[1]);
          const bookData = booksData.find((book) => book.id === bookIndex);

          if (bookData) {
            // 处理书籍名称 - 添加链接
            if (bookData.book && p.innerHTML.includes(bookData.book)) {
              p.innerHTML = p.innerHTML.replace(
                new RegExp(`(${bookData.book.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'g'),
                `<a href="#" class="book-link" onclick="event.preventDefault()">${bookData.book}</a>`
              );
            }

            // 处理出版社 - 添加链接
            if (bookData.publisher && p.innerHTML.includes(bookData.publisher)) {
              p.innerHTML = p.innerHTML.replace(
                new RegExp(`(${bookData.publisher.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'g'),
                `<a href="#" class="publisher-link" onclick="event.preventDefault()">${bookData.publisher}</a>`
              );
            }

            // 处理卷号 - 添加链接（如果不是 N/A）
            if (bookData.volume && bookData.volume !== 'N/A' && p.innerHTML.includes(bookData.volume)) {
              p.innerHTML = p.innerHTML.replace(
                new RegExp(`\\b(${bookData.volume.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'g'),
                `<a href="#" class="volume-link" onclick="event.preventDefault()">${bookData.volume}</a>`
              );
            }

            // 处理页码 - 添加链接
            if (bookData.pages && p.innerHTML.includes(bookData.pages)) {
              p.innerHTML = p.innerHTML.replace(
                new RegExp(`(${bookData.pages.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'g'),
                `<a href="#" class="pages-link" onclick="event.preventDefault()">${bookData.pages}</a>`
              );
            }

            // 处理年份 - 添加链接
            if (bookData.year && p.innerHTML.includes(`(${bookData.year})`)) {
              p.innerHTML = p.innerHTML.replace(
                new RegExp(`\\((${bookData.year})\\)`, 'g'),
                `(<a href="#" class="year-link" onclick="event.preventDefault()">${bookData.year}</a>)`
              );
            }

            // 在段落末尾添加 PDF 链接
            const pdfLink = document.createElement('a');
            pdfLink.href = `/v2/books/${bookIndex.toString().padStart(3, '0')}.pdf`;
            pdfLink.target = '_blank';
            pdfLink.className = 'pdf-link';
            pdfLink.textContent = '(PDF)';
            pdfLink.style.marginLeft = '0.5em';

            p.appendChild(document.createTextNode(' '));
            p.appendChild(pdfLink);
          }
        }
      });
    }
  }, [content, booksData, loading]);

  if (loading) {
    return (
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-4">{title}</h2>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-3 text-gray-600">Loading document...</span>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-4">{title}</h2>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 font-medium">Error loading document</p>
          <p className="text-red-500 text-sm mt-1">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </section>
    );
  }

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-bold mb-4">{title}</h2>
      <div
        ref={contentRef}
        className="books-document-content prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: content }}
      />

      <style jsx>{`
        .books-document-content {
          font-family: 'Times New Roman', Times, serif;
          line-height: 1.6;
          text-align: justify;
          color: #ffffff;
        }

        .books-document-content p {
          margin-bottom: 0.5rem;
          font-size: 16px;
          color: #ffffff;
        }

        .book-number {
          font-weight: bold;
          color: #1a1a1a;
          margin-right: 0.5em;
        }

        .author-highlight {
          font-weight: bold;
          color: #000;
        }

        .book-name {
          font-style: italic;
          color: #e67e22;
          font-weight: 500;
        }

        .book-name:hover {
          color: #d35400;
        }

        .books-document-content h1,
        .books-document-content h2,
        .books-document-content h3 {
          font-weight: bold;
          margin-top: 1.5rem;
          margin-bottom: 1rem;
          color: #1a1a1a;
        }

        .books-document-content h1 {
          font-size: 1.5rem;
          color: #ffffff;
        }

        .books-document-content h2 {
          font-size: 1.3rem;
          color: #ffffff;
        }

        .books-document-content h3 {
          font-size: 1.1rem;
          color: #ffffff;
        }

        .books-document-content a {
          color: #e67e22;
          text-decoration: none;
          transition: color 0.2s ease;
          font-weight: 500;
        }

        .books-document-content a:hover {
          color: #d35400;
          text-decoration: underline;
        }

        .book-link,
        .publisher-link,
        .volume-link,
        .pages-link,
        .year-link {
          color: #ff7f00;
          text-decoration: none;
          transition: color 0.2s ease;
          cursor: pointer;
        }

        .book-link:hover,
        .publisher-link:hover,
        .volume-link:hover,
        .pages-link:hover,
        .year-link:hover {
          color: #faac58;
          text-decoration: underline;
        }

        .pdf-link {
          color: #ff7f00;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.2s ease;
        }

        .pdf-link:hover {
          color: #faac58;
          text-decoration: underline;
        }

        .books-document-content ul,
        .books-document-content ol {
          margin-left: 1.5rem;
          margin-bottom: 1rem;
        }

        .books-document-content li {
          margin-bottom: 0.25rem;
        }

        .books-document-content table {
          width: 100%;
          border-collapse: collapse;
          margin: 1rem 0;
        }

        .books-document-content th,
        .books-document-content td {
          border: 1px solid #bdc3c7;
          padding: 0.5rem;
          text-align: left;
          color: #ffffff;
        }

        .books-document-content th {
          background-color: #ecf0f1;
          font-weight: bold;
          color: #ffffff;
        }
      `}</style>
    </section>
  );
}
