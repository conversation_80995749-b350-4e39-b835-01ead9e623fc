'use client';

import { useEffect, useRef, useState } from 'react';

interface PapersSectionProps {
  title?: string;
  documentPath?: string;
}

export default function PapersSection({ title = 'Papers', documentPath = '/v2/doc/pub.docx' }: PapersSectionProps) {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadDocument = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Loading document from:', documentPath);

        const response = await fetch(documentPath);
        if (!response.ok) {
          throw new Error(`Failed to fetch document: ${response.status} ${response.statusText}`);
        }

        console.log('Document fetched successfully, converting to array buffer...');
        const arrayBuffer = await response.arrayBuffer();
        console.log('Array buffer size:', arrayBuffer.byteLength);

        // 动态导入 mammoth 以避免服务器端问题
        const mammoth = await import('mammoth');

        // 配置 mammoth 选项以更好地处理 Word 文档
        const options = {
          convertImage: mammoth.images.imgElement((image: any) => {
            return image.read('base64').then((imageBuffer: string) => {
              return {
                src: 'data:' + image.contentType + ';base64,' + imageBuffer,
              };
            });
          }),
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Heading 2'] => h2:fresh",
            "p[style-name='Heading 3'] => h3:fresh",
            "p[style-name='Title'] => h1.title:fresh",
            "r[style-name='Strong'] => strong",
            "r[style-name='Emphasis'] => em",
          ],
        };

        console.log('Converting with mammoth...');
        const result = await mammoth.convertToHtml({ arrayBuffer }, options);
        console.log('Mammoth conversion completed');

        if (result.messages && result.messages.length > 0) {
          console.warn('Mammoth conversion messages:', result.messages);
        }

        // 处理和清理 HTML 内容
        let processedContent = result.value;

        // 移除空段落
        processedContent = processedContent.replace(/<p>\s*<\/p>/g, '');

        // 处理列表项
        processedContent = processedContent.replace(
          /<p>(\d+\.?\s*)(.*?)<\/p>/g,
          '<p><span class="paper-number">$1</span>$2</p>'
        );

        // 包装内容以应用自定义样式
        setContent(`<div class="papers-document-content">${processedContent}</div>`);
      } catch (error) {
        console.error('Error loading document:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setError(`Failed to load document: ${errorMessage}`);
        setContent('');
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [documentPath]);

  // 处理 Word 文档中的特殊格式
  useEffect(() => {
    if (contentRef.current && content && !loading) {
      const container = contentRef.current;

      // 处理编号列表
      const paragraphs = container.querySelectorAll('p');
      paragraphs.forEach((p) => {
        const text = p.textContent || '';
        const numberMatch = text.match(/^(\d+\.?\s*)/);

        if (numberMatch && !p.querySelector('.paper-number')) {
          // 创建编号元素
          const numberSpan = document.createElement('span');
          numberSpan.className = 'paper-number';
          numberSpan.textContent = numberMatch[1];

          // 更新段落内容
          p.innerHTML = p.innerHTML.replace(numberMatch[0], '');
          p.insertBefore(numberSpan, p.firstChild);
        }
      });

      // 处理作者名称加粗（GuanHua Chen）
      const textNodes = container.querySelectorAll('*');
      textNodes.forEach((node) => {
        if (node.textContent && node.textContent.includes('GuanHua Chen')) {
          node.innerHTML = node.innerHTML.replace(
            /GuanHua Chen/g,
            '<strong class="author-highlight">GuanHua Chen</strong>'
          );
        }
      });

      // 处理期刊名称斜体
      const journalPattern =
        /([A-Z][a-zA-Z\s&]+(?:Journal|Review|Letters|Science|Nature|Physical|Chemical|Materials|Applied|International))/g;
      paragraphs.forEach((p) => {
        if (p.innerHTML && !p.querySelector('em.journal-name')) {
          p.innerHTML = p.innerHTML.replace(journalPattern, '<em class="journal-name">$1</em>');
        }
      });
    }
  }, [content, loading]);

  if (loading) {
    return (
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-4">{title}</h2>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-3 text-gray-600">Loading document...</span>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-4">{title}</h2>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 font-medium">Error loading document</p>
          <p className="text-red-500 text-sm mt-1">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </section>
    );
  }

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-bold mb-4">{title}</h2>
      <div
        ref={contentRef}
        className="papers-document-content prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: content }}
      />

      <style jsx>{`
        .papers-document-content {
          font-family: 'Times New Roman', Times, serif;
          line-height: 1.6;
          text-align: justify;
          color: #ffffff;
        }

        .papers-document-content p {
          margin-bottom: 0.5rem;
          font-size: 16px;
          color: #ffffff;
        }

        .paper-number {
          font-weight: bold;
          color: #1a1a1a;
          margin-right: 0.5em;
        }

        .author-highlight {
          font-weight: bold;
          color: #000;
        }

        .journal-name {
          font-style: italic;
          color: #e67e22;
          font-weight: 500;
        }

        .journal-name:hover {
          color: #d35400;
        }

        .papers-document-content h1,
        .papers-document-content h2,
        .papers-document-content h3 {
          font-weight: bold;
          margin-top: 1.5rem;
          margin-bottom: 1rem;
          color: #1a1a1a;
        }

        .papers-document-content h1 {
          font-size: 1.5rem;
          color: #ffffff;
        }

        .papers-document-content h2 {
          font-size: 1.3rem;
          color: #ffffff;
        }

        .papers-document-content h3 {
          font-size: 1.1rem;
          color: #ffffff;
        }

        .papers-document-content a {
          color: #e67e22;
          text-decoration: none;
          transition: color 0.2s ease;
          font-weight: 500;
        }

        .papers-document-content a:hover {
          color: #d35400;
          text-decoration: underline;
        }

        .papers-document-content ul,
        .papers-document-content ol {
          margin-left: 1.5rem;
          margin-bottom: 1rem;
        }

        .papers-document-content li {
          margin-bottom: 0.25rem;
        }

        .papers-document-content table {
          width: 100%;
          border-collapse: collapse;
          margin: 1rem 0;
        }

        .papers-document-content th,
        .papers-document-content td {
          border: 1px solid #bdc3c7;
          padding: 0.5rem;
          text-align: left;
          color: #ffffff;
        }

        .papers-document-content th {
          background-color: #ecf0f1;
          font-weight: bold;
          color: #ffffff;
        }
      `}</style>
    </section>
  );
}
